extends HBoxContainer

# Dictionary mapping key types to their textures
var key_textures := {
	"ruby": Preloads.RUBY_KEY_IMAGE,
	"weird": Preloads.WEIRD_KEY_IMAGE,
	"brown": Preloads.BROWN_KEY_IMAGE,
	"gold": Preloads.GOLD_KEY_IMAGE,
	"emerald": Preloads.EMERALD_KEY_IMAGE,
	"silver": Preloads.SILVER_KEY_IMAGE
}

# Size for key icons in the UI
const KEY_ICON_SIZE := Vector2(24, 24)
const KEY_SPACING := 4
const COLUMN_SPACING := 8
const MAX_KEYS_PER_COLUMN := 3


func update_keys_ui(collected_keys: Array) -> void:
	# Clear existing key displays
	for node in get_children():
		remove_child(node)
		node.queue_free()

	# Return early if no keys to display
	if collected_keys.is_empty():
		return

	# collected_keys array: index 0 = oldest, last index = newest
	var total_keys = collected_keys.size()

	# First column: Contains only the oldest key (index 0)
	var first_column_container = _create_key_column(true) # true = single key column
	var first_column = first_column_container.get_child(0) as VBoxContainer
	add_child(first_column_container)

	# Add the oldest key to the first column
	var oldest_key = collected_keys[0]
	if oldest_key in key_textures:
		var key_texture_rect = _create_key_texture_rect(oldest_key)
		first_column.add_child(key_texture_rect)

	# If there are more keys, distribute them in subsequent columns
	if total_keys > 1:
		# Get remaining keys (excluding the oldest)
		var remaining_keys = collected_keys.slice(1) # All keys except index 0
		remaining_keys.reverse() # Reverse to get newest to oldest order

		# Calculate number of additional columns needed for remaining keys
		var remaining_key_count = remaining_keys.size()
		var additional_columns = ceili(float(remaining_key_count) / float(MAX_KEYS_PER_COLUMN))

		# Create additional columns and populate with remaining keys
		for column_index in range(additional_columns):
			var column_container = _create_key_column(false) # false = multi-key column
			var column = column_container.get_child(0) as VBoxContainer
			add_child(column_container)

			# Calculate key range for this column
			var start_key_index = column_index * MAX_KEYS_PER_COLUMN
			var end_key_index = min(start_key_index + MAX_KEYS_PER_COLUMN, remaining_key_count)

			# Add keys to this column (newest to oldest, top to bottom)
			for key_index in range(start_key_index, end_key_index):
				var key_type = remaining_keys[key_index]
				if key_type in key_textures:
					var key_texture_rect = _create_key_texture_rect(key_type)
					column.add_child(key_texture_rect)


func _create_key_column(is_single_key: bool = false) -> MarginContainer:
	"""Create a new VBoxContainer column for keys wrapped in a MarginContainer

	Args:
		is_single_key: If true, aligns to bottom (for single oldest key). If false, aligns to top (for multiple keys)
	"""
	var column = VBoxContainer.new()

	# Set alignment based on column type
	if is_single_key:
		column.alignment = BoxContainer.ALIGNMENT_END # Bottom alignment for single oldest key
	else:
		column.alignment = BoxContainer.ALIGNMENT_BEGIN # Top alignment for multiple keys

	# Add margin container for column spacing
	var margin_container = MarginContainer.new()
	margin_container.add_theme_constant_override("margin_right", COLUMN_SPACING)
	margin_container.add_child(column)

	return margin_container


func _create_key_texture_rect(key_type: String) -> MarginContainer:
	"""Create a TextureRect for a specific key type wrapped in a MarginContainer"""
	var key_texture_rect = TextureRect.new()
	key_texture_rect.texture = key_textures[key_type]
	key_texture_rect.custom_minimum_size = KEY_ICON_SIZE
	key_texture_rect.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
	key_texture_rect.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED

	# Add visual styling
	key_texture_rect.modulate = Color.WHITE

	# Add margin container for vertical spacing between keys
	var margin_container = MarginContainer.new()
	margin_container.add_theme_constant_override("margin_bottom", KEY_SPACING)
	margin_container.add_child(key_texture_rect)

	return margin_container


func get_collected_keys_from_level() -> Array:
	"""Get collected keys from the current level/map"""
	var level = get_tree().get_first_node_in_group("level")
	if level and "keys_collected" in level:
		return level.keys_collected
	return []


func refresh_display() -> void:
	"""Refresh the key display by getting keys from the current level"""
	var collected_keys = get_collected_keys_from_level()
	update_keys_ui(collected_keys)
