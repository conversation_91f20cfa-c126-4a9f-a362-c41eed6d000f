extends HBoxContainer

# Dictionary mapping key types to their textures
var key_textures := {
	"ruby": Preloads.RUBY_KEY_IMAGE,
	"weird": Preloads.WEIRD_KEY_IMAGE,
	"brown": Preloads.BROWN_KEY_IMAGE,
	"gold": Preloads.GOLD_KEY_IMAGE,
	"emerald": Preloads.EMERALD_KEY_IMAGE,
	"silver": Preloads.SILVER_KEY_IMAGE
}

# Size for key icons in the UI
const KEY_ICON_SIZE := Vector2(24, 24)
const KEY_SPACING := 4


func update_keys_ui(collected_keys: Array) -> void:
	# Clear existing key displays
	for node in get_children():
		remove_child(node)
		node.queue_free()

	# Create TextureRect nodes for each collected key
	for key_type in collected_keys:
		if key_type in key_textures:
			var key_texture_rect = TextureRect.new()
			key_texture_rect.texture = key_textures[key_type]
			key_texture_rect.custom_minimum_size = KEY_ICON_SIZE
			key_texture_rect.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
			key_texture_rect.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED

			# Add some visual styling
			key_texture_rect.modulate = Color.WHITE

			# Add margin container for spacing
			var margin_container = MarginContainer.new()
			margin_container.add_theme_constant_override("margin_right", KEY_SPACING)
			margin_container.add_child(key_texture_rect)

			add_child(margin_container)


func get_collected_keys_from_level() -> Array:
	"""Get collected keys from the current level/map"""
	var level = get_tree().get_first_node_in_group("level")
	if level and "keys_collected" in level:
		return level.keys_collected
	return []


func refresh_display() -> void:
	"""Refresh the key display by getting keys from the current level"""
	var collected_keys = get_collected_keys_from_level()
	update_keys_ui(collected_keys)
