extends HBoxContainer

# Dictionary mapping key types to their textures
var key_textures := {
	"ruby": Preloads.RUBY_KEY_IMAGE,
	"weird": Preloads.WEIRD_KEY_IMAGE,
	"brown": Preloads.BROWN_KEY_IMAGE,
	"gold": Preloads.GOLD_KEY_IMAGE,
	"emerald": Preloads.EMERALD_KEY_IMAGE,
	"silver": Preloads.SILVER_KEY_IMAGE
}

# Size for key icons in the UI
const KEY_ICON_SIZE := Vector2(24, 24)
const KEY_SPACING := 4
const COLUMN_SPACING := 8
const MAX_KEYS_PER_COLUMN := 3


func update_keys_ui(collected_keys: Array) -> void:
	# Clear existing key displays
	for node in get_children():
		remove_child(node)
		node.queue_free()

	# Return early if no keys to display
	if collected_keys.is_empty():
		return

	# collected_keys array: index 0 = oldest, last index = newest
	var total_keys = collected_keys.size()

	# Calculate number of columns needed
	var num_columns = ceili(float(total_keys) / float(MAX_KEYS_PER_COLUMN))

	# Create columns and distribute keys sequentially
	for column_index in range(num_columns):
		var column_container = _create_key_column()
		var column = column_container.get_child(0) as VBoxContainer
		add_child(column_container)

		# Calculate key range for this column
		var start_key_index = column_index * MAX_KEYS_PER_COLUMN
		var end_key_index = min(start_key_index + MAX_KEYS_PER_COLUMN, total_keys)

		# Get keys for this column and reverse them (oldest to newest becomes newest to oldest)
		var column_keys = collected_keys.slice(start_key_index, end_key_index)
		column_keys.reverse() # Now newest is first, oldest is last

		# Add keys to column (newest to oldest, top to bottom)
		for key_type in column_keys:
			if key_type in key_textures:
				var key_texture_rect = _create_key_texture_rect(key_type)
				column.add_child(key_texture_rect)


func _create_key_column() -> MarginContainer:
	"""Create a new VBoxContainer column for keys wrapped in a MarginContainer"""
	var column = VBoxContainer.new()
	column.alignment = BoxContainer.ALIGNMENT_BEGIN # Top alignment for all columns

	# Add margin container for column spacing
	var margin_container = MarginContainer.new()
	margin_container.add_theme_constant_override("margin_right", COLUMN_SPACING)
	margin_container.add_child(column)

	return margin_container


func _create_key_texture_rect(key_type: String) -> MarginContainer:
	"""Create a TextureRect for a specific key type wrapped in a MarginContainer"""
	var key_texture_rect = TextureRect.new()
	key_texture_rect.texture = key_textures[key_type]
	key_texture_rect.custom_minimum_size = KEY_ICON_SIZE
	key_texture_rect.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
	key_texture_rect.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED

	# Add visual styling
	key_texture_rect.modulate = Color.WHITE

	# Add margin container for vertical spacing between keys
	var margin_container = MarginContainer.new()
	margin_container.add_theme_constant_override("margin_bottom", KEY_SPACING)
	margin_container.add_child(key_texture_rect)

	return margin_container


func get_collected_keys_from_level() -> Array:
	"""Get collected keys from the current level/map"""
	var level = get_tree().get_first_node_in_group("level")
	if level and "keys_collected" in level:
		return level.keys_collected
	return []


func refresh_display() -> void:
	"""Refresh the key display by getting keys from the current level"""
	var collected_keys = get_collected_keys_from_level()
	update_keys_ui(collected_keys)
