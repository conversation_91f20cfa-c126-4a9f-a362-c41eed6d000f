[gd_scene load_steps=2 format=3 uid="uid://c87u0y4d0ssix"]

[ext_resource type="Script" uid="uid://cw750sp4c0uru" path="res://scenes/ui/hud/components/key_ui_container.gd" id="1_c755g"]

[node name="KeyUIContainer" type="HBoxContainer"]
alignment = 1
script = ExtResource("1_c755g")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 2

[node name="ColorRect2" type="ColorRect" parent="VBoxContainer"]
custom_minimum_size = Vector2(20, 20)
layout_mode = 2
color = Color(0, 1, 1, 1)

[node name="ColorRect" type="ColorRect" parent="VBoxContainer"]
custom_minimum_size = Vector2(20, 20)
layout_mode = 2

[node name="VBoxContainer2" type="VBoxContainer" parent="."]
layout_mode = 2
alignment = 1

[node name="ColorRect2" type="ColorRect" parent="VBoxContainer2"]
custom_minimum_size = Vector2(20, 20)
layout_mode = 2
color = Color(0.618954, 0, 0.320646, 1)

[node name="ColorRect" type="ColorRect" parent="VBoxContainer2"]
custom_minimum_size = Vector2(20, 20)
layout_mode = 2
color = Color(0, 0.461671, 0.204907, 1)
